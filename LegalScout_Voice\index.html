<!DOCTYPE html>
<html lang="en">
  <head>
    <!-- CRITICAL: CSP Meta Tag to Allow Eval (fixes module loading) -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: data: https://cdn.jsdelivr.net https://unpkg.com https://cdnjs.cloudflare.com https://cdn.vapi.ai https://vapi.ai https://c.daily.co https://*.daily.co https://o77906.ingest.sentry.io; script-src-elem 'self' 'unsafe-inline' blob: data: https://cdn.jsdelivr.net https://unpkg.com https://cdnjs.cloudflare.com https://cdn.vapi.ai https://vapi.ai https://c.daily.co https://*.daily.co https://o77906.ingest.sentry.io; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com https://c.daily.co https://*.daily.co; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data: blob: https:; media-src 'self' blob: data: https:; connect-src 'self' https://api.vapi.ai https://mcp.vapi.ai https://dashboard.vapi.ai https://cdn.vapi.ai https://vapi.ai https://utopqxsvudgrtiwenlzl.supabase.co https://c.daily.co https://*.daily.co https://o77906.ingest.sentry.io wss: ws:; frame-src 'self' https://c.daily.co https://*.daily.co; worker-src 'self' blob: data:; object-src 'none'; base-uri 'self';">

    <!-- FORCE NEW ASSISTANT: DISABLED - Breaks OAuth by clearing localStorage -->
    <!-- <script src="/force-new-assistant.js"></script> -->

    <!-- IMPORT.META POLYFILL: Must load first to prevent import.meta errors -->
    <script src="/import-meta-polyfill.js"></script>

    <!-- EMERGENCY API KEY FIX: Must load first to fix API key issues -->
    <script src="/emergency-api-key-fix.js"></script>

    <!-- CRITICAL PRODUCTION FIX: Must load first to fix all production issues -->
    <script src="/critical-production-fix.js"></script>

    <!-- SIMPLE FIX: Correct assistant ID and public key -->
    <script>
      // Simple fix: ensure both public and secret keys are available globally
      window.VITE_VAPI_PUBLIC_KEY = '310f0d43-27c2-47a5-a76d-e55171d024f7';
      window.VITE_VAPI_SECRET_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';
      window.VAPI_TOKEN = '6734febc-fc65-4669-93b0-929b31ff6564';
      console.log('✅ Vapi keys set globally');

      // Fix: Force correct assistant ID for damon subdomain
      const correctAssistantId = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';

      // Clear wrong assistant from localStorage and set correct one
      try {
        const attorneyData = JSON.parse(localStorage.getItem('attorney_571390ac-5a83-46b2-ad3a-18b9cf39d701') || '{}');
        if (attorneyData.assistant_id !== correctAssistantId) {
          attorneyData.assistant_id = correctAssistantId;
          localStorage.setItem('attorney_571390ac-5a83-46b2-ad3a-18b9cf39d701', JSON.stringify(attorneyData));
          console.log('✅ Fixed assistant ID in localStorage');
        }
      } catch (e) {
        console.log('Could not update localStorage:', e.message);
      }

      // Fix: Ensure Supabase key is available globally (needed to load assistant by domain)
      window.VITE_SUPABASE_URL = 'https://utopqxsvudgrtiwenlzl.supabase.co';
      window.VITE_SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU';
      window.VITE_SUPABASE_ANON_KEY = window.VITE_SUPABASE_KEY;
      console.log('✅ Supabase keys set globally - should load correct assistant by domain');
    </script>

    <!-- CONSOLIDATED: All critical fixes in one script to prevent conflicts -->
    <!-- DISABLED: Replaced by definitive-auth-fix.js -->
    <!-- <script src="/consolidated-dashboard-fix.js"></script> -->
    <!-- VAPI OFFICIAL PATTERN: Disabled - now using installed package via vapiLoader -->
    <!-- <script src="/vapi-official-pattern-fix.js"></script> -->

    <!-- EMERGENCY INLINE CRITICAL FIXES - Guaranteed to load first -->
    <script>
      console.log('🚀 [EMERGENCY] Starting emergency critical fixes...');

      // Fix 1: Process polyfill
      if (typeof window !== 'undefined' && typeof window.process === 'undefined') {
        console.log('🔧 [EMERGENCY] Adding process polyfill');
        window.process = {
          env: { NODE_ENV: 'development' },
          browser: true,
          version: '',
          versions: { node: '' }
        };
        console.log('✅ [EMERGENCY] Process polyfill added');
      }

      // Fix 2: Development mode detection
      const isDev = window.location.hostname === 'localhost' ||
                   window.location.hostname === '127.0.0.1' ||
                   window.location.port === '5174' ||
                   window.location.port === '5173';

      // Override to production mode for clean deployment
      window.process.env.NODE_ENV = 'production';
      console.log('🔧 [EMERGENCY] Development mode:', false, '(forced production)');

      // Fix 3: Fetch URL rewriting + Supabase API key preservation
      if (typeof window.fetch === 'function') {
        const origFetch = window.fetch;
        window.fetch = function(url, opts = {}) {
          let fixedUrl = url;

          if (isDev && typeof url === 'string') {
            if (url.includes('https://dashboard.legalscout.net')) {
              fixedUrl = url.replace('https://dashboard.legalscout.net', window.location.origin);
              console.log('🔧 [EMERGENCY] Fixed production URL:', url, '→', fixedUrl);
            }

            if (url.includes('[object Object]')) {
              console.warn('⚠️ [EMERGENCY] Fixed malformed URL:', url);
              fixedUrl = window.location.origin + '/api/health';
            }

            if (url.startsWith('/api/')) {
              fixedUrl = window.location.origin + url;
              console.log('🔧 [EMERGENCY] Fixed relative URL:', url, '→', fixedUrl);
            }
          }

          // DISABLED: All fetch interceptors removed - letting Supabase handle its own auth
          // The clean-auth-solution.js script restores original fetch and removes all conflicts

          return origFetch(fixedUrl, opts);
        };
        console.log('✅ [EMERGENCY] Fetch patched with Supabase API key preservation');
      }

      console.log('🎉 [EMERGENCY] Emergency fixes complete!');
    </script>
    <!-- PRODUCTION MODE: Minimal fix scripts for clean deployment -->
    <!-- Only essential fixes for production-like environment -->
    <!-- <script src="/critical-fixes-v2.js?v=2"></script> -->
    <!-- <script src="/fix-api-base-url.js"></script> -->
    <!-- <script src="/fix-process-undefined.js"></script> -->
    <!-- <script src="/clear-invalid-attorney-data.js"></script> -->
    <!-- <script src="/fix-s-catch-standalone.js"></script> -->
    <!-- <script src="/fix-promise-catch.js"></script> -->
    <!-- <script src="/fix-websocket-connection.js"></script> -->
    <!-- Fix for API errors - Must be the third script -->
    <!-- <script src="/fix-api-errors.js"></script> -->
    <!-- Fix for React context timeout - Must load before other React-related scripts -->
    <!-- <script src="/fix-react-context-timeout.js"></script> -->
    <!-- Fix for standalone attorney manager - Let's disable this outer fix script too -->
    <!-- <script src="/fix-standalone-attorney-manager.js"></script> -->
    <!-- Attorney validation fix - Already commented -->
    <!-- <script src="/fix-attorney-validation.js"></script> -->
    <!-- 🛡️ ROBUST STATE HANDLER: The automated attorney state resolver -->
    <script src="/robust-state-handler.js"></script>
    <!-- CRITICAL: Disable automatic assistant creation to prevent Vapi overrun -->
    <script src="/disable-automatic-assistant-creation.js"></script>
    <!-- Controlled assistant creation system for manual creation -->
    <script src="/controlled-assistant-creation.js"></script>
    <!-- COMMENTED OUT: Replaced by consolidated fix to prevent conflicts -->
    <!-- <script src="/fix-dashboard-assistant-creation.js"></script> -->
    <!-- <script src="/fix-attorney-loading-issues.js"></script> -->
    <!-- PRODUCTION MODE: Enable essential scripts for dashboard functionality -->
    <script src="/standalone-attorney-manager-fixed.js"></script>
    <script src="/fix-validate-attorney-data.js"></script>
    <!-- <script src="/fix-attorney-undefined.js"></script> -->
    <script src="/fix-vapi-assistant-switch.js"></script>
    <script src="/enhance-attorney-manager.js"></script>
    <script src="/fix-enhance-attorney-manager.js"></script>
    <script src="/fix-vapi-assistant-config.js"></script>
    <!-- COMMENTED OUT: Replaced by consolidated fix to prevent conflicts -->
    <!-- <script src="/fix-assistant-id-loading.js"></script> -->
    <!-- <script src="/fix-mcp-connection.js"></script> -->
    <!-- <script src="/fix-dashboard-saving.js"></script> -->
    <!-- <script src="/fix-banner-functionality.js"></script> -->
    <!-- <script src="/fix-banner-removal-persistence.js"></script> -->
    <!-- <script src="/fix-agent-tab-logo-restoration.js"></script> -->
    <!-- <script src="/fix-banner-upload-interface.js"></script> -->
    <script src="/unified-banner-fix.js"></script>
    <!-- <script src="/immediate-label-fix.js"></script> -->
    <!-- <script src="/vapi-mock-mcp-service.js"></script> -->
    <!-- <script src="/fix-vapi-mcp-connection.js"></script> -->
    <!-- <script src="/fix-label-input-mismatch.js"></script> -->
    <!-- Debug tool for label/input issues - Disabled to reduce console noise -->
    <!-- <script src="/debug-labels.js"></script> -->
    <!-- Targeted fix for specific label issue - Disabled to reduce console noise -->
    <!-- <script src="/find-label-issue.js"></script> -->
    <!-- Manual label check tool - Disabled to reduce console noise -->
    <!-- <script src="/manual-label-check.js"></script> -->
    <!-- PRODUCTION MODE: Disable development-specific scripts -->
    <!-- <script src="/set-test-subdomain.js"></script> -->
    <!-- <script src="/fix-configure-button.js"></script> -->
    <!-- <script src="/agent-configure-button.js"></script> -->
    <!-- Attorney State Initializer - Already commented -->
    <!-- <script src="/attorney-state-initializer.js"></script> -->
    <!-- Simplified Vapi Integration - Likely conflicts -->
    <!-- <script src="/simplified-vapi-integration.js"></script> -->
    <!-- Supabase Realtime Sync - Already commented -->
    <!-- <script src="/supabase-realtime-sync.js"></script> -->
    <!-- Attorney Bridge - Already commented -->
    <!-- <script src="/attorney-bridge.js"></script> -->
    <meta charset="UTF-8" />
    <!-- No framer-motion patches needed anymore - using Vite plugin instead -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>LegalScout</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" />
    <!-- PRODUCTION MODE: Disable Three.js CDN loading to prevent conflicts -->
    <!-- Three.js will be loaded by the application modules as needed -->
    <!-- <script>
      // Only load Three.js if not already loaded to prevent multiple instances
      if (typeof THREE === 'undefined') {
        const script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js';
        script.onload = function() {
          console.log('Three.js loaded from CDN');
          // Load Three-Globe after Three.js is loaded
          const globeScript = document.createElement('script');
          globeScript.src = 'https://unpkg.com/three-globe@2.42.1/dist/three-globe.min.js';
          document.head.appendChild(globeScript);
        };
        document.head.appendChild(script);
      }
    </script> -->

    <!-- ROBUST VAPI SDK LOADER: Disabled - now using installed package via vapiLoader -->
    <!-- <script src="/vapi-sdk-loader.js"></script> -->

    <!-- DASHBOARD IFRAME MANAGER: Enhanced iframe communication -->
    <script src="/dashboard-iframe-manager.js"></script>

    <!-- PRODUCTION CORS FIX: Comprehensive fix for CORS and API endpoint issues -->
    <script src="/production-cors-fix.js"></script>

    <!-- CLEAN AUTHENTICATION SOLUTION: Removes all fetch interceptors and lets Supabase handle auth -->
    <script src="/clean-auth-solution.js"></script>

    <!-- CALL SYNC DIAGNOSTIC: Browser-based diagnostic for call sync issues -->
    <script src="/call-sync-diagnostic.js"></script>

    <!-- PRODUCTION VAPI DEBUG: Comprehensive debugging for Vapi API key issues -->
    <script src="/production-debug-vapi.js"></script>

    <!-- DISABLED: All fetch interceptors removed to prevent conflicts -->
    <!-- <script src="/definitive-auth-fix.js"></script> -->
    <!-- <script src="/final-supabase-fix.js"></script> -->
    <!-- <script src="/fix-invalid-jwt-claim.js"></script> -->

    <!-- Load Supabase from CDN -->
    <script>
      // Only create Supabase client if not already created to prevent multiple instances
      if (!window.supabase) {
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2';
        script.onload = function() {
          if (window.location.hostname === 'localhost' || window.location.hostname.includes('localhost')) {
            console.log('Supabase loaded from CDN');
          }

          // Initialize Supabase client
          const supabaseUrl = 'https://utopqxsvudgrtiwenlzl.supabase.co';
          const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU';

          // Create Supabase client
          if (typeof supabase !== 'undefined' && typeof supabase.createClient === 'function') {
            if (window.location.hostname === 'localhost' || window.location.hostname.includes('localhost')) {
              console.log('Creating Supabase client from CDN');
            }
            window.supabase = supabase.createClient(supabaseUrl, supabaseKey);
            if (window.location.hostname === 'localhost' || window.location.hostname.includes('localhost')) {
              console.log('Supabase client created from CDN');
            }
          } else {
            console.warn('Supabase not available from CDN');
          }
        };
        document.head.appendChild(script);
      } else if (window.location.hostname === 'localhost' || window.location.hostname.includes('localhost')) {
        console.log('Supabase client already exists, skipping CDN load');
      }
    </script>

    <!-- Pre-fix scripts (Potential cause of issues) -->
    <!-- Commenting out problematic script -->
    <!-- <script src="/pre-fix-attorney-id-v2.js"></script> -->
    <!-- PRODUCTION MODE: Disable React context fix -->
    <!-- <script src="/react-context-fix.js"></script> -->
    <!-- Auth Context Fix - Disable to prevent conflict -->
    <!-- <script src="/auth-context-fix.js"></script> -->
    <!-- Attorney State Context Fix - Disable to prevent conflict -->
    <!-- <script src="/attorney-state-context-fix.js"></script> -->
    <!-- Vercel-specific Framer Motion Fix - Must be the very first script -->
    <!-- <script src="/vercel-framer-fix.js"></script> -->
    <!-- Production-specific Framer Motion Fix - Must load second -->
    <!-- <script src="/production-framer-fix.js"></script> -->
    <!-- Direct patch for LayoutGroupContext.mjs - Must load third -->
    <!-- <script src="/layout-group-context-patch.js"></script> -->
    <!-- Enhanced Framer Motion Fix - Must load fourth -->
    <!-- <script src="/enhanced-framer-fix.js"></script> -->
    <!-- Test script to verify our fix is working -->
    <!-- <script src="/test-framer-fix.js"></script> -->
    <!-- Fix for interaction issues -->
    <!-- <script src="/interaction-fix.js"></script> -->
    <!-- Fix for URL submission -->
    <!-- <script src="/url-submit-fix.js"></script> -->
    <!-- Fix for Auto-Configure button -->
    <!-- <script src="/auto-configure-fix.js"></script> -->
    <!-- Direct fix for Auto-Configure button -->
    <!-- <script src="/auto-configure-direct-fix.js"></script> -->
    <!-- Fix for improper headers -->
    <!-- <script src="/headers-fix.js"></script> -->
    <!-- Fix for authentication state management -->
    <!-- <script src="/fix-auth-state.js"></script> -->
    <!-- Attorney creation fix -->
    <!-- <script src="/fix-attorney-creation.js"></script> -->
    <!-- Fix for sync tools -->
    <!-- <script src="/sync-tools-fix.js"></script> -->
    <!-- Fix for preview controls visibility -->
    <!-- <script src="/preview-controls-fix.js"></script> -->
    <!-- Fix for attorney state manager -->
    <!-- <script src="/attorney-state-manager-fix.js"></script> -->
    <!-- Enhanced fix for attorney state manager -->
    <!-- <script src="/fix-attorney-state-manager.js"></script> -->
    <!-- Fix for Supabase connection - Let's disable this too, rely on main client -->
    <!--
    <script>
      // Ensure Supabase is properly initialized
      (function() {
        console.log('[SupabaseConnectionFix] Starting fix...');

        // Check if Supabase is already initialized
        if (window.supabase) {
          console.log('[SupabaseConnectionFix] Supabase already initialized');
          return;
        }

        // Wait for Supabase to be initialized
        const checkInterval = setInterval(() => {
          if (window.supabase) {
            clearInterval(checkInterval);
            console.log('[SupabaseConnectionFix] Supabase initialized');
            return;
          }

          // Try to initialize Supabase manually
          try {
            // Supabase URL and key
            const supabaseUrl = 'https://utopqxsvudgrtiwenlzl.supabase.co';
            const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU';

            // Check if Supabase client is available
            if (typeof supabase !== 'undefined' && typeof supabase.createClient === 'function') {
              console.log('[SupabaseConnectionFix] Creating Supabase client manually');

              // Create Supabase client
              window.supabase = supabase.createClient(supabaseUrl, supabaseKey);

              console.log('[SupabaseConnectionFix] Supabase client created manually');
              clearInterval(checkInterval);
            }
          } catch (error) {
            console.error('[SupabaseConnectionFix] Error initializing Supabase:', error);
          }
        }, 100);

        // Clear interval after 10 seconds
        setTimeout(() => {
          clearInterval(checkInterval);
          console.warn('[SupabaseConnectionFix] Timed out waiting for Supabase');
        }, 10000);
      })();
    </script>
    -->
    <!-- Direct fix for the S function - Must load before other fix scripts -->
    <!-- <script src="/fix-s-function.js"></script> -->
    <!-- Fix for form field interactions - Specifically targets the attorney name field -->
    <!-- <script src="/fix-form-interactions.js"></script> -->
    <!-- Fix for Qr component - Specifically targets the component causing the S(...).catch error -->
    <!-- <script src="/fix-qr-component.js"></script> -->
    <!-- Additional attorney validation fix - Already commented -->
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
    <script>
      // Remove any existing persistent call container
      (function() {
        function removePersistentCallContainer() {
          const persistentCallContainer = document.getElementById('persistent-call-container');
          if (persistentCallContainer) {
            persistentCallContainer.parentNode.removeChild(persistentCallContainer);
            console.log('Removed persistent call container');
          }
        }

        // Remove immediately
        removePersistentCallContainer();

        // Also set up a MutationObserver to remove it if it gets added again
        const observer = new MutationObserver(function(mutations) {
          mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
              mutation.addedNodes.forEach(function(node) {
                if (node.id === 'persistent-call-container' ||
                    (node.nodeType === 1 && node.querySelector('#persistent-call-container'))) {
                  removePersistentCallContainer();
                }
              });
            }
          });
        });

        // Start observing the document body for changes
        observer.observe(document.body, { childList: true, subtree: true });
      })();
    </script>
  </body>
</html>